package com.adins.esign.webservices.model.external;

import com.adins.framework.service.base.model.MssRequestType;

public class UpdateDataSignerExternalRequest extends MssRequestType {
	private static final long serialVersionUID = 1L;
	
	private String psreCode;
	private String nik;
	private String phoneNo;
	private String email;
	private String ipAddress;
	
	public String getPsreCode() {
		return psreCode;
	}
	
	public void setPsreCode(String psreCode) {
		this.psreCode = psreCode;
	}
	
	public String getNik() {
		return nik;
	}
	
	public void setNik(String nik) {
		this.nik = nik;
	}
	
	public String getPhoneNo() {
		return phoneNo;
	}
	
	public void setPhoneNo(String phoneNo) {
		this.phoneNo = phoneNo;
	}
	
	public String getEmail() {
		return email;
	}
	
	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getIpAddress() {
		return ipAddress;
	}
	
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
}
