package com.adins.esign.businesslogic.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.mail.MessagingException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsOtpLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.UserExternalLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.TekenAjaConstant;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsNotificationtypeoftenant;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.model.custom.ActivationDigisignResponseBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.RequestSentOtpSigningRequest;
import com.adins.esign.webservices.model.RequestSentOtpSigningResponse;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalRequest;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalResponse;
import com.adins.esign.webservices.model.external.GetActivationLinkRequest;
import com.adins.esign.webservices.model.external.GetActivationLinkResponse;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalRequest;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpRequestSigningResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterStatusResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.PrivyException;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
public class GenericUserExternalLogic extends BaseLogic implements UserExternalLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericUserExternalLogic.class);

	private static final String VFIRST_ERR28681 = "28681";
	private static final String VFIRST_ERR28682 = "28682";
	private static final String VFIRST_ERR408 = "408";
	private static final String KEY_FULLNAME = "fullname";

	@Value("${esign.regex.phone}") private String regexPhone;
	@Value("${esign.regex.phone.custom}") private String regexPhoneCustom;
	@Value("${esign.regex.email}") private String regexEmail;
	@Value("${spring.mail.username}") private String fromEmailAddr;

	@Autowired private TenantLogic tenantLogic;
	@Autowired private PrivyGeneralLogic privyGeneralLogic;
	@Autowired private TekenAjaLogic tekenAjaLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private SmsOtpLogic smsOtpLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;

	@Override
	public CheckVerificationStatusExternalResponse checkVerificationStatusExternal(CheckVerificationStatusExternalRequest request, String xApiKey, AuditContext audit) {
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		if (StringUtils.isBlank(request.getTrxNo())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"trxNo"}, audit), ReasonParam.MANDATORY_PARAM);
		}

		TrBalanceMutation balanceMutation = daoFactory.getBalanceMutationDao().getBalanceMutationByTrxNoAndTenant(request.getTrxNo(), tenant);
		if (null == balanceMutation
				|| !GlobalVal.CODE_LOV_BALANCE_TYPE_VRF.equals(balanceMutation.getMsLovByLovBalanceType().getCode())
				|| !GlobalVal.VENDOR_CODE_PRIVY_ID.equals(balanceMutation.getMsVendor().getVendorCode())) {
			// Kalau API ini sudah support PSrE selain PRIVY, bisa hapus pengecekan vendor di atas
			throw new ParameterException(getMessage("businesslogic.global.datanotfound1", new String[] {"Trx No"}, audit), ReasonParam.INVALID_CONDITION);
		}

		MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(balanceMutation.getMsTenant(), balanceMutation.getMsVendor());

		try {
			PrivyGeneralRegisterStatusResponse checkResponse = privyGeneralLogic.checkRegisterStatus(vendoroftenant, balanceMutation, audit);
			String message = privyGeneralLogic.getRegisterStatusMessage(checkResponse, audit);
			RegisterVerificationStatusBean verifResults = privyGeneralLogic.getCheckRegisterVerificationResults(checkResponse, audit);

			if ("waiting_verification".equalsIgnoreCase(checkResponse.getData().getStatus())) {

				Status status = new Status();
				status.setCode(StatusCode.PRIVY_ERROR);
				status.setMessage(message);

				CheckVerificationStatusExternalResponse response = new CheckVerificationStatusExternalResponse();
				response.setStatus(status);
				response.setResults(verifResults);
				return response;
			}

			if ("rejected".equalsIgnoreCase(checkResponse.getData().getStatus()) || "invalid".equalsIgnoreCase(checkResponse.getData().getStatus())) {
				Status status = new Status();
				status.setCode(StatusCode.PRIVY_ERROR);
				status.setMessage(message);

				CheckVerificationStatusExternalResponse response = new CheckVerificationStatusExternalResponse();
				response.setStatus(status);
				response.setVerifStatus(false);
				response.setResults(verifResults);
				return response;
			}

			// Successful verification
			Status status = new Status();
			status.setCode(0);
			status.setMessage(message);

			CheckVerificationStatusExternalResponse response = new CheckVerificationStatusExternalResponse();
			response.setStatus(status);
			response.setVerifStatus(true);
			response.setResults(verifResults);
			return response;

		} catch (Exception e) {

			LOG.error("Check register status General Privy error: {}", e.getLocalizedMessage(), e);

			Status status = new Status();
			status.setCode(StatusCode.PRIVY_ERROR);
			status.setMessage(e.getLocalizedMessage());

			CheckVerificationStatusExternalResponse response = new CheckVerificationStatusExternalResponse();
			response.setStatus(status);
			response.setResults(new RegisterVerificationStatusBean());
			return response;

		}

	}

	@Override
	public GetActivationLinkResponse getActivationLinkExternal(GetActivationLinkRequest request, String xApiKey, AuditContext audit) {
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		GetActivationLinkResponse response = new GetActivationLinkResponse();
		Status status = new Status();


		MsVendorRegisteredUser vUser = userValidatorLogic.validateGetVrUserByNikForActivationLinkExternal(request.getIdKtp(), audit);

		if (GlobalVal.VENDOR_CODE_TEKENAJA.equals(vUser.getMsVendor().getVendorCode())) {
			TknajRegisterCekResponse responsetekenaja = tekenAjaLogic.registerCek(request.getIdKtp(),
					TekenAjaConstant.REGCEK_ACTION_RESEND_EMAIL, vUser.getMsVendor(), tenant, audit);

			validateTknAjRegisterCek(responsetekenaja,vUser, status, audit);
		}
		else if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vUser.getMsVendor().getVendorCode())) {
			MsVendoroftenant vendorTenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vUser.getMsVendor());

			UserBean userBean = new UserBean();
			userBean.setEmail(vUser.getSignerRegisteredEmail());
			userBean.setAdminEmail(vendorTenant.getEmailPartner());
			userBean.setToken(vendorTenant.getToken());

			ActivationDigisignResponseBean resultActivationBean = digisignLogic.getActivationLink(userBean,
					new AuditContext());

			if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultActivationBean.getResult())) {
				String errorMessage;
				if ("14".equals(resultActivationBean.getResult())
						&& "Email sudah melakukan aktivasi".equals(resultActivationBean.getNotif())) {

					PersonalDataBean personalBean = daoFactory.getUserDao().getUserDataByIdMsUser(vUser.getAmMsuser().getIdMsUser(), false);

					String detailUser = "(" + personalBean.getIdNoRaw() + ", " + vUser.getAmMsuser().getFullName() + ")";

					errorMessage = "Anda sudah selesai aktivasi, silahkan menunggu permintaan tanda tangan dikirimkan untuk lanjut ke proses berikutnya.";
					errorMessage = errorMessage.replace("Anda", "User " + detailUser);

					Date activatedDate = new Date();
					Date expiredDate = DateUtils.addYears(activatedDate, 1);
					expiredDate = DateUtils.addMinutes(expiredDate, -10);

					vUser.setIsRegistered("1");
					vUser.setIsActive("1");
					vUser.setActivatedDate(activatedDate);
					vUser.setCertExpiredDate(expiredDate);
					vUser.setUsrUpd(vUser.getSignerRegisteredEmail());
					vUser.setDtmUpd(new Date());
					daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vUser);

				} else {
					errorMessage = this.messageSource.getMessage("businesslogic.user.erroractivation",
							new Object[] { resultActivationBean.getNotif() }, this.retrieveLocaleAudit(audit));
				}

				status.setCode(StatusCode.DIGISIGN_FAILED);
				status.setMessage(errorMessage);
				response.setStatus(status);
				return response;
			}
			else {
				status.setCode(0);
				status.setMessage(resultActivationBean.getLink());
			}
		}
		else{
			vUser.setIsActive("1");
			vUser.setUsrUpd(vUser.getSignerRegisteredEmail());
			vUser.setDtmUpd(new Date());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vUser);

			throw new UserException(this.messageSource.getMessage("businesslogic.user.dontneedactivate",new Object[] { vUser.getMsVendor().getVendorName() } ,
					this.retrieveLocaleAudit(audit)), ReasonUser.NOT_NEED_ACTIVATE_VENDOR);
		}

		response.setStatus(status);

		return response;
	}

	private void validateTknAjRegisterCek(TknajRegisterCekResponse responsetekenaja,MsVendorRegisteredUser vUser, Status status, AuditContext audit) {
		if (responsetekenaja.getCode() != null) {
			if (responsetekenaja.getCode().equals(GlobalVal.TEKEN_REGCEK_USER_DO_NOT_EXISTS)) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED,
								new String[] { "User" }, this.retrieveLocaleAudit(audit)),
						ReasonUser.NOT_REGISTERED);
			} else if (responsetekenaja.getCode().equals(GlobalVal.TEKEN_REGCEK_USER_EXIST_VERIFIED)) {
				throw new UserException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_ALREADY_ACTIVATED,
								new String[] { "User" }, this.retrieveLocaleAudit(audit)),
						ReasonUser.ALREADY_REGISTERED);
			}
		}
		else {
			if ("1".equals(vUser.getEmailService())) {
				status.setCode(0);
				status.setMessage(getMessage("businesslogic.external.tknajaemailserviceone", null, audit));
			}
			else {
				status.setCode(0);
				status.setMessage(getMessage("businesslogic.external.tknajaemailservicezero", null, audit));
			}
		}
	}

	private void validatePhoneParam(String phone, AuditContext audit) {
		if (StringUtils.isBlank(phone)) {
			throw new UserException(getMessage("businesslogic.user.phonenocannotbeempty", null, audit),
					ReasonUser.PHONE_NO_EMPTY);
		}

		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue() : regexPhone;
		if (!phone.matches(phoneNoRegex)) {
			throw new UserException(getMessage("businesslogic.user.invalidphonenoformat", null, audit),
					ReasonUser.INVALID_FORMAT);
		}
	}

	private void validateEmailParam(String email, AuditContext audit) {
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue() : regexEmail;
		if (StringUtils.isNotBlank(email) && !email.matches(emailRegex)) {
			throw new UserException(getMessage("businesslogic.user.invalidemailformat", null, audit),
					ReasonUser.INVALID_FORMAT);
		}
	}

	private String normalizePhoneNumber(String phoneNumber) {
		if (StringUtils.isBlank(phoneNumber)) {
			return phoneNumber;
		}

		if (phoneNumber.startsWith("+628")) {
			return "08" + phoneNumber.substring(4);
		} else if (phoneNumber.startsWith("628")) {
			return "08" + phoneNumber.substring(3);
		}

		return phoneNumber;
	}

	private boolean isAllDocumentSigned(List<TrDocumentD> documents, AmMsuser user) {

		for (int i = 0; i < documents.size(); i++) {
			List<TrDocumentDSign> listTrDocumentDSign = daoFactory.getDocumentDao().getDocumentDSignByIdDocumentDAndIdUser(documents.get(i).getIdDocumentD(), user.getIdMsUser());
			for (int j = 0; j < listTrDocumentDSign.size(); j++) {
				if (listTrDocumentDSign.get(j).getSignDate() == null) {
					return false;
				}
			}
		}

		return true;
	}

	private MsVendorRegisteredUser validateUserAndDocumentForOtpSignExternal(TrDocumentH documentH, MsTenant tenant, RequestSentOtpSigningRequest request, AuditContext audit) {
		AmMsuser phoneUser = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), false, audit);
		if (null == phoneUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND,
					new Object[] { request.getPhoneNo() }, audit), ReasonUser.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
		}

		if (StringUtils.isNotBlank(request.getEmail())) {
			userValidatorLogic.validateUserByPhoneWithEmail(phoneUser.getIdMsUser(), request.getPhoneNo(), request.getEmail(), false, audit);
		}

		if (StringUtils.isBlank(request.getRefNumber())) {
			throw new DocumentException(getMessage("businesslogic.document.referencenoempty", null, audit), ReasonDocument.REFERENCE_NO_EMPTY);
		}

		String vendorCode = daoFactory.getVendorDao().getVendorCodeByIdDocumentH(documentH.getIdDocumentH());
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(phoneUser.getIdMsUser(), vendorCode);
		if (null == vendorUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit), ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		if (!"1".equals(vendorUser.getIsActive())) {
			throw new UserException(getMessage("businesslogic.user.userwithemailhasnotactivated", new Object[] {request.getEmail()}, audit),
					ReasonUser.USER_HAS_NOT_ACTIVATED);
		}

		if (userValidatorLogic.isCertifExpiredForSign(vendorUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { vendorCode }, audit), ReasonUser.USER_CERTIFICATE_EXPIRED);
		}

		List<TrDocumentD> listTrDocumentD = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		Long[] listIdDocumentD = new Long[listTrDocumentD.size()];

		for (int i = 0; i < listTrDocumentD.size(); i++) {
			listIdDocumentD[i] = listTrDocumentD.get(i).getIdDocumentD();
		}

		BigInteger checkUserSign = daoFactory.getDocumentDao().getDistinctSingerByIdDocumentDAndIdMsUser(listIdDocumentD, phoneUser.getIdMsUser());
		if (checkUserSign == null) {
			throw new DocumentException(getMessage("businesslogic.document.userisnotthesignerofthecontract2", new Object[] {tenant.getRefNumberLabel(), request.getRefNumber()}, audit),
					ReasonDocument.USER_IS_NOT_THE_SIGNER_OF_THE_CONTRACT);
		}

		if (documentH.getTotalDocument().equals(documentH.getTotalSigned())) {
			throw new DocumentException(getMessage("businesslogic.document.contractalreadysigned2", new Object[] {tenant.getRefNumberLabel(), request.getRefNumber()}, audit),
					ReasonDocument.CONTRACT_ALREADY_SIGNED);
		}

		boolean checkAllDocumentSigned = isAllDocumentSigned(listTrDocumentD, phoneUser);
		if (checkAllDocumentSigned) {
			throw new DocumentException(getMessage("businesslogic.document.alldocumentalreadysigned", new Object[] {request.getRefNumber()}, audit),
					ReasonDocument.DOCUMENT_ALREADY_SIGNED_ALL);
		}

		if ((documentH.getSigningProcess() != null) && (documentH.getSigningProcess().equals("1"))) {
			throw new DocumentException(getMessage("businesslogic.document.contractisinsigningprocess2", new Object[] {tenant.getRefNumberLabel(), request.getRefNumber()}, audit),
					ReasonDocument.CONTRACT_IS_IN_SIGNING_PROCESS);
		}

		return vendorUser;
	}

	private String sendOtpPrivyGeneralAndReturnTrxNo(String phoneNo, MsVendorRegisteredUser msVendorRegisteredUser, AmMsuser user, MsTenant tenant, MsVendor vendor, List<String> documentId, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {

		if (StringUtils.isBlank(msVendorRegisteredUser.getVendorRegistrationId())) {
			throw new PrivyException(getMessage(GlobalKey.MESSAGE_ERROR_PRIVY_IDNOTFOUND, null, audit));
		}

		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

		TrDocumentD docDNewest = new TrDocumentD();

		List<String> documentIdsOtpRequestPrivy = new ArrayList<>();
		for (int i = 0; i < documentId.size(); i++ ) {

			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId.get(i));
			if (null == docDNewest.getRequestDate() || docD.getRequestDate().after(docDNewest.getRequestDate())  ) {
				docDNewest = docD;
			}

			String documentIdAlphanumeric = documentId.get(i).replace("-", "");
			documentIdsOtpRequestPrivy.add(documentIdAlphanumeric);
		}

		String documentIds = String.join(", ", documentIdsOtpRequestPrivy);
		LOG.info("About to send Privy OTP for document IDs: {}", documentIds);

		TrPsreSigningConfirmation psreSigningConfirmation = new TrPsreSigningConfirmation(user, documentIds, null);
		psreSigningConfirmation.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		psreSigningConfirmation.setDtmCrt(new Date());

		daoFactory.getDocumentDao().insertPsreSigningConfirmation(psreSigningConfirmation);

		MsLov lovNotificationMedia = new MsLov();
		lovNotificationMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);

		PrivyGeneralOtpRequestSigningResponse otpRequestSigning = privyGeneralLogic.otpRequestSigning(msVendorRegisteredUser.getVendorRegistrationId(), tenant, vendor, documentIdsOtpRequestPrivy, audit);
		if (otpRequestSigning.getError() != null) {

			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
			auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
			auditTrail.setOtpCode(auditTrailBean.getOtpCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
			auditTrail.setNotes(auditTrailBean.getNotes());
			auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
			auditTrail.setNotificationVendor(GlobalVal.VENDOR_CODE_PRIVY_ID);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			if (auditTrailBean.getDocumentDs() != null ) {
				for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);;
				}
			}

			String errorMessage = privyGeneralLogic.buildOtpRequestSigningErrorMessage(otpRequestSigning, audit);
			throw new PrivyException(errorMessage);
		}

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(auditTrailBean.getEmail());
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
		auditTrail.setNotificationVendor(GlobalVal.VENDOR_CODE_PRIVY_ID);
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		if (auditTrailBean.getDocumentDs() != null ) {
			for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(docD);
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);;
			}
		}

		psreSigningConfirmation.setTransactionId(otpRequestSigning.getData().getTransactionId());
		daoFactory.getDocumentDao().updatePsreSigningConfirmation(psreSigningConfirmation);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);

		String notes = phoneNo + GlobalVal.SEND_OTP_SMS;

		saldoLogic.insertBalanceMutation(null, docDNewest.getTrDocumentH(), null, balanceType, trxType, tenant, vendor, new Date(), docDNewest.getTrDocumentH().getRefNumber(),
				-1, trxNo, user, notes, null, audit);

		return trxNo;
	}

	private RequestSentOtpSigningResponse sendOtpSignPrivy(RequestSentOtpSigningRequest request, MsTenant tenant, TrDocumentH documentH, MsVendorRegisteredUser vendorUser, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		if (request.getDocumentId() == null) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit), ReasonDocument.EMPTY_DOCUMENT_ID);
		}

		int isDocumentIdInDocumentH = 0;

		for (int i = 0; i < request.getDocumentId().size(); i++ ) {
			TrDocumentD documentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId().get(i));
			if (documentH.getIdDocumentH() == documentD.getTrDocumentH().getIdDocumentH()) {
				isDocumentIdInDocumentH = 1;
			}
		}

		if (isDocumentIdInDocumentH == 0) {
			throw new DocumentException(getMessage("businesslogic.document.documentdoesnotbelongtorefnumber", null, audit),
					ReasonDocument.DOCUMENT_NOT_BELONG_TO_REF_NUMBER);
		}

		AmMsuser user = vendorUser.getAmMsuser();
		MsVendor vendor = vendorUser.getMsVendor();
		String trxNo = this.sendOtpPrivyGeneralAndReturnTrxNo(request.getPhoneNo(), vendorUser, user, tenant, vendor, request.getDocumentId(), auditTrailBean, audit);

		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		if (null == user.getResetCodeRequestDate() || null == user.getResetCodeRequestNum() || !DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			resetCodeRequestNum = 0;
		}

		resetCodeRequestNum++;
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setResetCodeRequestDate(new Date());
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		Status status = new Status();
		status.setCode(0);

		RequestSentOtpSigningResponse response = new RequestSentOtpSigningResponse();
		response.setStatus(status);
		response.setTrxNo(trxNo);
		response.setOtpByEmail("0");
		response.setPsreCode(vendor.getVendorCode());
		return response;
	}

	private RequestSentOtpSigningResponse sendOtpSignWithSmsVfirst(MsTenant tenant, TrDocumentH documentH, MsVendorRegisteredUser vendorUser, String phoneNo, SigningProcessAuditTrailBean auditTraiBean, AuditContext audit) {

		MsVendor balmutVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, tenant, balmutVendor, audit);

		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);

		auditTraiBean.setOtpCode(otpCode);

		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		MsMsgTemplate template = null;
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0){
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		}else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}


		SendSmsResponse responseSms = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, template.getBody(), tenant);
		if (gs.getGsValue().equals("1")) {
			responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean, auditTraiBean);
		} else {
			LOG.info("Send SMS OTP Success");
			responseSms.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		String trxNo = String.valueOf(responseSms.getTrxNo());

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = phoneNo + GlobalVal.SEND_OTP_SMS_SIGNING;
		AmMsuser user = vendorUser.getAmMsuser();

		String refNumber = documentH != null ? documentH.getRefNumber() : null;
		if (responseSms.getErrorCode() == null ||
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, documentH, null, balanceType, trxType, tenant, vendor, new Date(), refNumber,
					-1, trxNo, user, notes, responseSms.getGuid(), audit);
		} else {
			saldoLogic.insertBalanceMutation(null, documentH, null, balanceType, trxType, tenant, vendor, new Date(), refNumber,
					0, trxNo, user, notes + GlobalVal.BALMUT_ERROR + responseSms.getErrorCode(), responseSms.getGuid(), audit);
		}

		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		if (null == user.getResetCodeRequestDate() || null == user.getResetCodeRequestNum() || !DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			resetCodeRequestNum = 0;
		}

		resetCodeRequestNum++;
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setResetCodeRequestDate(new Date());
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		Status status = new Status();
		status.setCode(0);

		RequestSentOtpSigningResponse response = new RequestSentOtpSigningResponse();
		response.setStatus(status);
		response.setTrxNo(trxNo);
		response.setOtpByEmail("0");
		response.setPsreCode(vendorUser.getMsVendor().getVendorCode());
		return response;
	}

	private RequestSentOtpSigningResponse sendOtpSignWithSmsJatis(MsTenant tenant, TrDocumentH documentH, MsVendorRegisteredUser vendorUser, String phoneNo, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		MsVendor balmutVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, tenant, balmutVendor, audit);

		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);

		auditTrailBean.setOtpCode(otpCode);

		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);

		MsMsgTemplate template = null;
		String trxNo;
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		} else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}

		AmMsuser user = vendorUser.getAmMsuser();
		MsVendor vendor = vendorUser.getMsVendor();
		if ("1".equals(gs.getGsValue())) {
			trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			String notes = phoneNo + GlobalVal.SEND_OTP_SMS_SIGNING;
			JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, phoneNo, template.getBody(), String.valueOf(trxNo), true);
			jatisSmsLogic.sendSmsAndCutBalance(request, documentH, null, user, notes, audit, auditTrailBean);
		} else {
			trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			LOG.info("Send SMS OTP Jatis Success");
		}

		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		if (null == user.getResetCodeRequestDate() || null == user.getResetCodeRequestNum() || !DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			resetCodeRequestNum = 0;
		}

		resetCodeRequestNum++;
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setResetCodeRequestDate(new Date());
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		Status status = new Status();
		status.setCode(0);

		RequestSentOtpSigningResponse response = new RequestSentOtpSigningResponse();
		response.setStatus(status);
		response.setTrxNo(trxNo);
		response.setOtpByEmail("0");
		response.setPsreCode(vendor.getVendorCode());
		return response;
	}

	private RequestSentOtpSigningResponse sendOtpSignWithWhatsAppJatis(MsTenant tenant, TrDocumentH documentH, MsVendorRegisteredUser vendorUser, String phoneNo, SigningProcessAuditTrailBean auditTraiBean, AuditContext audit) {

		MsVendor balmutVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP, tenant, balmutVendor, audit);

		MsMsgTemplate templateWa = null;

		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage("businesslogic.tenant.msgtemplatewithotpactiveduration", new Object[] { tenant.getOtpActiveDuration() }, audit),
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}

		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		AmMsuser user = vendorUser.getAmMsuser();
		String notes = phoneNo + GlobalVal.SEND_OTP_WA_SIGNING;

		auditTraiBean.setOtpCode(otpCode);

		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			SendWhatsAppRequest sendWhatsappRequest = new SendWhatsAppRequest();
			sendWhatsappRequest.setTemplate(templateWa);
			sendWhatsappRequest.setBodyTexts(bodyTexts);
			sendWhatsappRequest.setButtonText(buttonText);
			sendWhatsappRequest.setReservedTrxNo(trxNo);
			sendWhatsappRequest.setPhoneNumber(phoneNo);
			sendWhatsappRequest.setAmMsuser(user);
			sendWhatsappRequest.setMsTenant(tenant);
			sendWhatsappRequest.setRemoveHeader(true);
			sendWhatsappRequest.setTrDocumentH(documentH);
			sendWhatsappRequest.setNotes(notes);
			sendWhatsappRequest.setIsOtp(true);
			whatsAppLogic.sendMessage(sendWhatsappRequest, auditTraiBean, audit);
		} else {
			LOG.info("Send WA OTP Sign success (dummy)");
		}

		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		if (null == user.getResetCodeRequestDate() || null == user.getResetCodeRequestNum() || !DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			resetCodeRequestNum = 0;
		}

		resetCodeRequestNum++;
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setResetCodeRequestDate(new Date());
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		Status status = new Status();
		status.setCode(0);

		RequestSentOtpSigningResponse response = new RequestSentOtpSigningResponse();
		response.setStatus(status);
		response.setTrxNo(trxNo);
		response.setOtpByEmail("0");
		response.setPsreCode(vendorUser.getMsVendor().getVendorCode());
		return response;
	}

	private RequestSentOtpSigningResponse sendOtpSignWithWhatsAppHalosis(MsTenant tenant, TrDocumentH documentH, MsVendorRegisteredUser vendorUser, String phoneNo, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {

		MsVendor balmutVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP, tenant, balmutVendor, audit);

		MsMsgTemplate templateWa = null;

		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage("businesslogic.tenant.msgtemplatewithotpactiveduration", new Object[] { tenant.getOtpActiveDuration() }, audit),
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}

		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		AmMsuser user = vendorUser.getAmMsuser();
		String notes = phoneNo + GlobalVal.SEND_OTP_WA_SIGNING;

		auditTrailBean.setOtpCode(otpCode);

		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			HalosisSendWhatsAppRequestBean sendWhatsappHalosisRequest = new HalosisSendWhatsAppRequestBean();
			sendWhatsappHalosisRequest.setTemplate(templateWa);
			sendWhatsappHalosisRequest.setBodyTexts(bodyTexts);
			sendWhatsappHalosisRequest.setButtonText(buttonText);
			sendWhatsappHalosisRequest.setReservedTrxNo(trxNo);
			sendWhatsappHalosisRequest.setPhoneNumber(phoneNo);
			sendWhatsappHalosisRequest.setAmMsuser(vendorUser.getAmMsuser());
			sendWhatsappHalosisRequest.setMsTenant(tenant);
			sendWhatsappHalosisRequest.setTrDocumentH(documentH);
			sendWhatsappHalosisRequest.setNotes(notes);
			sendWhatsappHalosisRequest.setIsOtp(true);
			whatsAppHalosisLogic.sendMessage(sendWhatsappHalosisRequest, auditTrailBean, audit);
		}
		else {
			LOG.info("Send WA OTP HALOSIS  Signing success");
		}

		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		if (null == user.getResetCodeRequestDate() || null == user.getResetCodeRequestNum() || !DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			resetCodeRequestNum = 0;
		}

		resetCodeRequestNum++;
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setResetCodeRequestDate(new Date());
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		Status status = new Status();
		status.setCode(0);

		RequestSentOtpSigningResponse response = new RequestSentOtpSigningResponse();
		response.setStatus(status);
		response.setTrxNo(trxNo);
		response.setOtpByEmail("0");
		response.setPsreCode(vendorUser.getMsVendor().getVendorCode());
		return response;
	}

	private RequestSentOtpSigningResponse sendOtpSignWithEmail(MsTenant tenant, MsVendorRegisteredUser vendorUser, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {

		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		AmMsuser user = vendorUser.getAmMsuser();

		auditTrailBean.setOtpCode(otpCode);

		Map<String, Object> templateParameters = new HashMap<>();
		Map<String, Object> userMap = new HashMap<>();
		userMap.put(KEY_FULLNAME, vendorUser.getAmMsuser().getFullName());
		userMap.put("otp", otpCode);

		MsMsgTemplate template = null;
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0 ) {
			userMap.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			templateParameters.put("user", userMap);
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_VERIF_EMAIL_WITH_DURATION, templateParameters);
		} else {
			templateParameters.put("user", userMap);
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP, templateParameters);
		}

		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(new String[] { vendorUser.getSignerRegisteredEmail() });
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		try {
			emailSenderLogic.sendEmail(emailInfo, null, auditTrailBean);
			LOG.info("Send OTP Email to: {}", vendorUser.getSignerRegisteredEmail());
		} catch (MessagingException e) {
			LOG.error("Failed to send OTP Email", e);
		}

		Short resetCodeRequestNum = user.getResetCodeRequestNum();
		if (null == user.getResetCodeRequestDate() || null == user.getResetCodeRequestNum() || !DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			resetCodeRequestNum = 0;
		}

		resetCodeRequestNum++;
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setResetCodeRequestDate(new Date());
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		daoFactory.getUserDao().updateUser(user);

		Status status = new Status();
		status.setCode(0);

		RequestSentOtpSigningResponse response = new RequestSentOtpSigningResponse();
		response.setStatus(status);
		response.setOtpByEmail("1");
		response.setPsreCode(vendorUser.getMsVendor().getVendorCode());
		return response;
	}

	private NotificationType getNotificationTypeByRequestMessageMedia(String sendingPoint, MsTenant tenant) {
		MsNotificationtypeoftenant tenantNotifType = daoFactory.getNotificationtypeoftenantDao().getNotificationType(tenant, NotificationSendingPoint.OTP_SIGN_EXTERNAL.toString());

		MsLov smsGateway = null;
		if (tenantNotifType == null || tenantNotifType.getLovSmsGateway() == null) {
			smsGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
		} else {
			smsGateway = tenantNotifType.getLovSmsGateway();
		}

		MsLov waGateway = null;
		if (tenantNotifType == null || tenantNotifType.getLovWaGateway() == null) {
			waGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
		} else {
			waGateway = tenantNotifType.getLovWaGateway();
		}

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equalsIgnoreCase(sendingPoint)) {
			return GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS.equals(smsGateway.getCode()) ? NotificationType.SMS_JATIS : NotificationType.SMS_VFIRST;
		}

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equalsIgnoreCase(sendingPoint)) {
			return GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS.equals(waGateway.getCode()) ? NotificationType.WHATSAPP_HALOSIS : NotificationType.WHATSAPP;
		}

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equalsIgnoreCase(sendingPoint)) {
			return NotificationType.EMAIL;
		}

		return null;
	}

	private RequestSentOtpSigningResponse sendOtpSignByRequestMessageMedia(String sendingPoint, MsTenant tenant, MsVendorRegisteredUser vendorUser, TrDocumentH documentH, String phone, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equalsIgnoreCase(sendingPoint) && "1".equals(vendorUser.getEmailService())) {
			throw new UserException(getMessage("businesslogic.user.userregisterwithoutemail", null, audit), ReasonUser.USER_REGISTERED_WITHOUT_EMAIL);
		}

		NotificationType notificationType = getNotificationTypeByRequestMessageMedia(sendingPoint, tenant);
		return sendOtpSignBasedOnNotifType(notificationType, tenant, vendorUser, documentH, phone, auditTrailBean, audit);
	}

	private RequestSentOtpSigningResponse sendOtpSignByDbMessageMedia(MsTenant tenant, MsVendorRegisteredUser vendorUser, TrDocumentH documentH, String phone, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		NotificationType notificationType = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.OTP_SIGN_EXTERNAL, vendorUser.getEmailService());
		return sendOtpSignBasedOnNotifType(notificationType, tenant, vendorUser, documentH, phone, auditTrailBean, audit);
	}

	private RequestSentOtpSigningResponse sendOtpSignBasedOnNotifType(NotificationType notificationType, MsTenant tenant, MsVendorRegisteredUser vendorUser, TrDocumentH documentH, String phone, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		if (NotificationType.SMS_JATIS == notificationType) {
			return sendOtpSignWithSmsJatis(tenant, documentH, vendorUser, phone, auditTrailBean, audit);
		}

		if (NotificationType.SMS_VFIRST == notificationType) {
			return sendOtpSignWithSmsVfirst(tenant, documentH, vendorUser, phone, auditTrailBean, audit);
		}

		if (NotificationType.WHATSAPP == notificationType) {
			return sendOtpSignWithWhatsAppJatis(tenant, documentH, vendorUser, phone, auditTrailBean, audit);
		}

		if (NotificationType.WHATSAPP_HALOSIS == notificationType) {
			return sendOtpSignWithWhatsAppHalosis(tenant, documentH, vendorUser, phone, auditTrailBean, audit);
		}

		return sendOtpSignWithEmail(tenant, vendorUser, auditTrailBean, audit);
	}

	@Override
	public UpdateDataSignerExternalResponse updateDataSignerExternal(UpdateDataSignerExternalRequest request, String xApiKey, AuditContext audit) {
		if (StringUtils.isBlank(request.getPsreCode())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"psreCode"}, audit), ReasonParam.MANDATORY_PARAM);
		}

		if (StringUtils.isBlank(request.getNik())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"NIK"}, audit), ReasonParam.MANDATORY_PARAM);
		}

		if (!StringUtils.isNumeric(request.getNik())) {
			throw new ParameterException(getMessage("businesslogic.user.nikisnotnumber", null, audit), ReasonParam.NIK_MUST_BE_NUMERIC);
		}

		if (request.getNik().length() != 16) {
			throw new ParameterException(getMessage("businesslogic.external.idnoinvalid", null, audit), ReasonParam.INVALID_NIK_LENGTH);
		}

		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getPsreCode(), true, audit);

		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(request.getNik());

		if (user == null) {
			Status status = new Status();
			status.setCode(404);
			status.setMessage(getMessage("businesslogic.external.idnotfound", new Object[] {request.getNik()}, audit));

			UpdateDataSignerExternalResponse response = new UpdateDataSignerExternalResponse();
			response.setStatus(status);
			return response;
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), vendor.getVendorCode());

		if (vendorUser == null) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit), ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		MsUseroftenant useroftenant = daoFactory.getTenantDao().getUseroftenantByLoginIdTenantCode(user.getLoginId(), tenant.getTenantCode());
		if (useroftenant == null) {
			throw new UserException(getMessage("businesslogic.user.usernotregisteredintenant",
					null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_TENANT);
		}

		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), true);

		user.setHashedIdNo(MssTool.getHashedString(request.getNik()));

		if (StringUtils.isNotBlank(request.getEmail())) {
			validateEmailParam(request.getEmail(), audit);

			if (!StringUtils.equalsIgnoreCase(vendorUser.getSignerRegisteredEmail(), request.getEmail())) {
				AmMsuser existingUser = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), false, audit);
				if (existingUser != null && existingUser.getIdMsUser() != user.getIdMsUser()) {
					Status status = new Status();
					status.setCode(409);
					status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_EMAIL_ALREADY_EXISTED, new Object[] {request.getEmail()}, audit));

					UpdateDataSignerExternalResponse response = new UpdateDataSignerExternalResponse();
					response.setStatus(status);
					return response;
				}

				vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
				vendorUser.setEmailService("0");

				user.setLoginId(StringUtils.upperCase(request.getEmail()));
				personalData.getUserPersonalData().setEmail(StringUtils.upperCase(request.getEmail()));
				user.setEmailService("1");
			}
		}

		if (StringUtils.isNotBlank(request.getPhoneNo())) {
			if (!request.getPhoneNo().matches(regexPhoneCustom)) {
				throw new UserException(getMessage("businesslogic.user.invalidphonenoformat", null, audit),
						ReasonUser.INVALID_FORMAT);
			}

			String normalizedPhoneNo = normalizePhoneNumber(request.getPhoneNo());

			String oldPhone = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());

			if (!StringUtils.equals(oldPhone, normalizedPhoneNo)) {
				List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByPhone(normalizedPhoneNo);
				if (!idMsusers.isEmpty()) {
					for (Map<String, Object> idMsuser : idMsusers) {
						BigInteger idMsuser1 = (BigInteger) idMsuser.get("d0");
						if (idMsuser1.longValue() != user.getIdMsUser()) {
							Status status = new Status();
							status.setCode(409);
							status.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_ALREADY_EXISTED, new Object[] {normalizedPhoneNo}, audit));

							UpdateDataSignerExternalResponse response = new UpdateDataSignerExternalResponse();
							response.setStatus(status);
							return response;
						}
					}
				}

				user.setHashedPhone(MssTool.getHashedString(normalizedPhoneNo));
				vendorUser.setHashedSignerRegisteredPhone(MssTool.getHashedString(normalizedPhoneNo));
				vendorUser.setPhoneBytea(personalDataEncLogic.encryptFromString(normalizedPhoneNo));
				personalData.setPhoneRaw(normalizedPhoneNo);
				personalData.getUserPersonalData().setPhoneBytea(personalDataEncLogic.encryptFromString(normalizedPhoneNo));
			}
		}

		personalData.getUserPersonalData().setDtmUpd(new Date());
		personalData.getUserPersonalData().setUsrUpd(audit.getCallerId());

		user.setDtmUpd(new Date());
		user.setUsrUpd(audit.getCallerId());

		vendorUser.setDtmUpd(new Date());
		vendorUser.setUsrUpd(audit.getCallerId());

		daoFactory.getUserDao().updateUserNewTran(user);
		daoFactory.getUserDao().updateUserPersonalDataNewTrans(personalData);
		daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);

		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_UPDATE_SIGNER_DATA);

		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(vendorUser.getPhoneBytea());
		auditTrail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());

		if (StringUtils.isNotBlank(vendorUser.getSignerRegisteredEmail())) {
			auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
		}

		auditTrail.setAmMsUser(user);
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendor);
		auditTrail.setLovProcessType(processType);
		auditTrail.setResultStatus("1");

		StringBuilder notesBuilder = new StringBuilder("Update Signer Data");
		if (StringUtils.isNotBlank(request.getPhoneNo())) {
			notesBuilder.append(" No HP ").append(phoneNumber);
		}
		if (StringUtils.isNotBlank(request.getEmail())) {
			notesBuilder.append(" Email ").append(vendorUser.getSignerRegisteredEmail());
		}

		auditTrail.setNotes(notesBuilder.toString());
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(audit.getCallerId());

		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

		AmMsuser requester = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);

		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_EDIT_SIGNER_DATA);

		TrUserDataAccessLog accessLog = new TrUserDataAccessLog();
		accessLog.setDtmCrt(new Date());
		accessLog.setUsrCrt(audit.getCallerId());
		accessLog.setUserDataAccessed(user);
		accessLog.setUserRequest(requester);
		accessLog.setAccessDate(new Date());
		accessLog.setLovActionType(actionType);
		accessLog.setIpAddress(StringUtils.isNotBlank(request.getIpAddress()) ? request.getIpAddress() : GlobalVal.IP_ADDRESS_LOCAL);

		StringBuilder requestDetails = new StringBuilder("Edit data");
		
		accessLog.setRequestDetails(requestDetails.toString());

		daoFactory.getUserDataAccessLogDao().insertUserDataAccessLogNewTrx(accessLog);

		Status status = new Status();
		status.setCode(0);
		status.setMessage(getMessage("businesslogic.user.updatesuccess", null, audit));

		UpdateDataSignerExternalResponse response = new UpdateDataSignerExternalResponse();
		response.setStatus(status);
		return response;
	}

	@Override
	public RequestSentOtpSigningResponse sendOtpExternal(RequestSentOtpSigningRequest request, String xApiKey, AuditContext audit) {

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());
		if (documentH == null) {
			throw new DocumentException(getMessage("businesslogic.document.agreementnotfoundintenant2", new Object[] {tenant.getRefNumberLabel(), request.getRefNumber(), tenant.getTenantCode()}, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		validatePhoneParam(request.getPhoneNo(), audit);
		validateEmailParam(request.getEmail(), audit);
		MsVendorRegisteredUser vendorUser = validateUserAndDocumentForOtpSignExternal(documentH, tenant, request, audit);

		List<TrDocumentD> docDs = new ArrayList<>();

		if (request.getDocumentId() != null && request.getDocumentId().stream().anyMatch(id -> id != null && !id.trim().isEmpty())) {
			for (String documentId : request.getDocumentId()) {
				TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
				docDs.add(docD);
			}
		} else {
			docDs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		}


		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP);
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_EXTERNAL);

		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();

		auditTrailBean.setDocumentDs(docDs);
		auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setNotes("Request OTP Signing");
		auditTrailBean.setPhone(request.getPhoneNo());
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(vendorUser.getAmMsuser());
		auditTrailBean.setVendorPsre(vendorUser.getMsVendor());

		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendorUser.getMsVendor().getVendorCode())) {
			return sendOtpSignPrivy(request, tenant, documentH, vendorUser, auditTrailBean, audit);
		}

		if (StringUtils.isNotBlank(request.getSendingPointOption())) {
			return sendOtpSignByRequestMessageMedia(request.getSendingPointOption(), tenant, vendorUser, documentH, request.getPhoneNo(), auditTrailBean, audit);
		}

		return sendOtpSignByDbMessageMedia(tenant, vendorUser, documentH, request.getPhoneNo(), auditTrailBean, audit);

	}

}
