package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.interfacing.VidaLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.SignatureDetailBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.model.custom.vida.GenerateTokenResponseBean;
import com.adins.esign.model.custom.vida.PoaErrorBean;
import com.adins.esign.model.custom.vida.PoaSignerBean;
import com.adins.esign.model.custom.vida.PoaSigningInfoAppearanceBean;
import com.adins.esign.model.custom.vida.PoaSigningInfoBean;
import com.adins.esign.model.custom.vida.RequestInfoBean;
import com.adins.esign.model.custom.vida.VidaFieldBean;
import com.adins.esign.model.custom.vida.VidaRegisterConsentBean;
import com.adins.esign.model.custom.vida.VidaRegisterResponseContainer;
import com.adins.esign.util.MssTool;
import com.adins.esign.util.VidaUtils;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.vida.PoaRequest;
import com.adins.esign.webservices.model.vida.PoaResponse;
import com.adins.esign.webservices.model.vida.PoaStatusCheckResponse;
import com.adins.esign.webservices.model.vida.VidaRegisterRequest;
import com.adins.esign.webservices.model.vida.VidaRegisterResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.VidaException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.password.PasswordHash;
import com.google.gson.Gson;

@Component
public class GenericVidaLogic extends BaseLogic implements VidaLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericVidaLogic.class);
	
	@Autowired private Gson gson;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	
	@Value("${vida.generatetoken.uri}") private String generateTokenUri;
	@Value("${vida.register.uri}") private String registerUri;
	@Value("${vida.poa.uri}") private String poaUri;
	@Value("${vida.poa.checkstatus.uri}") private String poaCheckstatusUri;
	@Value("${vida.generatetokenpoa.uri}") private String generateTokenPoaUri;
	
	private static final Double LIVENESS_THRESHOLD = 0.95;
	
	private static final String VERIF_RESULT_TRUE = "TRUE";
	
	private static final String FIELD_POB = "pob";
	private static final String FIELD_FULL_NAME = "full_name";
	private static final String FIELD_DOB = "dob";
	private static final String FIELD_ADDRESS = "address";
	private static final String FIELD_PROVINCE = "province";
	private static final String FIELD_CITY = "city";
	private static final String FIELD_DISTRICT = "district";
	private static final String FIELD_FAMILY_CARD_NO = "family_card_no";
	private static final String FIELD_VILLAGE = "village";
	private static final String FIELD_MOTHER_MAIDEN_NAME = "mother_maiden_name";
	private static final String FIELD_SELFIE = "selfiePhoto";
	private static final String FIELD_NIK = "nik";
	
	private static final String CONST_LOG_POAPAUSED = "PoA paused.";
	
	@Override
	public String generateToken(MsVendor vendor, MsTenant tenant) throws IOException {
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		String toEncode = vot.getClientId() + ":" + vot.getClientSecret();
		String auth = Base64.getEncoder().encodeToString(toEncode.getBytes());
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, "Basic " + auth);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED);
		
		MultivaluedMap<String, Object> formData = new MultivaluedHashMap<>();
		formData.add("grant_type", "client_credentials");
		formData.add("scope", "roles");
		
		WebClient client = WebClient.create(generateTokenUri).headers(mapHeader);
		
		Date start = new Date();
		Response response = client.form(formData);
		Date end = new Date();
		logProcessDuration("Get VIDA token", start, end);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String generateToken = IOUtils.toString(isReader);
		LOG.info("Get VIDA token for {} response: {}", tenant.getTenantName(), generateToken);
		GenerateTokenResponseBean token = gson.fromJson(generateToken, GenerateTokenResponseBean.class);
        return token.getAccessToken();
	}
	
	public String generateTokenPoa(MsTenant tenant, AuditContext audit) throws IOException {
		MsTenantSettings clientId = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_CLIENT_ID_POA_VIDA);
		MsTenantSettings clientSecret = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_CLIENT_SECRET_POA_VIDA);
		commonValidatorLogic.validateNotNull(clientId, getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_NOT_FOUND, new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_CLIENT_ID_POA_VIDA, tenant.getTenantName()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		commonValidatorLogic.validateNotNull(clientSecret, getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_NOT_FOUND, new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_CLIENT_SECRET_POA_VIDA, tenant.getTenantName()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		commonValidatorLogic.validateNotNull(clientId.getSettingValue(), getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_VALUE_EMPTY, new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_CLIENT_ID_POA_VIDA}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		commonValidatorLogic.validateNotNull(clientSecret.getSettingValue(), getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_VALUE_EMPTY, new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_CLIENT_SECRET_POA_VIDA}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		
		String toEncode = clientId.getSettingValue() + ":" + clientSecret.getSettingValue();
		String auth = Base64.getEncoder().encodeToString(toEncode.getBytes());
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, "Basic " + auth);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED);
		
		MultivaluedMap<String, Object> formData = new MultivaluedHashMap<>();
		formData.add("grant_type", "client_credentials");
		formData.add("scope", "roles");
		
		WebClient client = WebClient.create(generateTokenPoaUri).headers(mapHeader);
		
		Date start = new Date();
		Response response = client.form(formData);
		Date end = new Date();
		logProcessDuration("Get VIDA PoA token", start, end);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String generateToken = IOUtils.toString(isReader);
		LOG.info("Get VIDA PoA token for {} response: {}", tenant.getTenantName(), generateToken);
		GenerateTokenResponseBean token = gson.fromJson(generateToken, GenerateTokenResponseBean.class);
        return token.getAccessToken();
	}

	@Override
	public String cvvEncryption(MsVendorRegisteredUser vru, MsTenant tenant, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, UnsupportedEncodingException {

		MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GENERALSETTING_API_KEY_VIDA_CVV);
		commonValidatorLogic.validateNotNull(ts, getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_NOT_FOUND, new Object[] {AmGlobalKey.GENERALSETTING_API_KEY_VIDA_CVV, tenant.getTenantName()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		commonValidatorLogic.validateNotNull(ts.getSettingValue(), getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_VALUE_EMPTY, new Object[] {AmGlobalKey.GENERALSETTING_API_KEY_VIDA_CVV}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		
		long epochNow = System.currentTimeMillis()/1000;
	    String cvvEpoch = vru.getVendorUserAutosignCvv() + epochNow ;
		return VidaUtils.encrytDataAes(cvvEpoch, vru.getVendorUserAutosignKey(), ts.getSettingValue());
	}
	
	private VidaRegisterRequest buildVidaRegisterRequest(UserBean userData, Date consentTime, String reservedTrxNo) {
		
		VidaRegisterConsentBean consent = new VidaRegisterConsentBean();
		consent.setConsentedAt(String.valueOf(consentTime.getTime() / 1000));
		consent.setConsentGiven(true);
		
		VidaRegisterRequest registerRequest = new VidaRegisterRequest();
		registerRequest.setPartnerTrxId(reservedTrxNo);
		registerRequest.setEmail(userData.getEmail());
		registerRequest.setMobile(MssTool.changePrefixToPlus62(userData.getUserPhone()));
		registerRequest.setFullName(userData.getUserName());
		registerRequest.setDob(userData.getUserDob());
		registerRequest.setGovId(userData.getIdNo());
		registerRequest.setGovIdType("KTP");
		registerRequest.setSelfiePhoto(userData.getSelfPhoto());
		registerRequest.setConsent(consent);
		return registerRequest;
	}
	
	private VidaRegisterRequest buildExternalVidaRegisterRequest(RegisterExternalRequest request, Date consentTime, String reservedTrxNo) {
		VidaRegisterConsentBean consent = new VidaRegisterConsentBean();
		consent.setConsentedAt(String.valueOf(consentTime.getTime() / 1000));
		consent.setConsentGiven(true);
		
		VidaRegisterRequest registerRequest = new VidaRegisterRequest();
		registerRequest.setPartnerTrxId(reservedTrxNo);
		registerRequest.setEmail(request.getEmail());
		registerRequest.setMobile(MssTool.changePrefixToPlus62(request.getTlp()));
		registerRequest.setFullName(request.getNama());
		registerRequest.setDob(request.getTglLahir());
		registerRequest.setGovId(request.getIdKtp());
		registerRequest.setGovIdType("KTP");
		registerRequest.setSelfiePhoto(MssTool.cutImageStringPrefix(request.getSelfPhoto()));
		registerRequest.setConsent(consent);
		return registerRequest;
	}

	@Override
	public VidaRegisterResponseContainer register(UserBean userData, MsTenant tenant, Date consentTime, String reservedTrxNo, AuditContext audit) {
		try {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_VIDA);
			String token = generateToken(vendor, tenant);
			
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, HttpHeaders.buildBearerToken(token));
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			LOG.info("Vida register request header: {}", mapHeader);
			WebClient client = WebClient.create(registerUri).headers(mapHeader);
			
			// Prepare body
			VidaRegisterRequest registerRequest = buildVidaRegisterRequest(userData, consentTime, reservedTrxNo);
			String jsonRequest = gson.toJson(registerRequest);
			LOG.info("Vida register request: {}", jsonRequest);
			
			// Get response
			Response response = client.post(jsonRequest);
			LOG.info("Vida register response code: {} {}", response.getStatus(), response.getStatusInfo().getReasonPhrase());
			
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String jsonResponse = IOUtils.toString(isReader);
			LOG.info("Vida register response: {}", jsonResponse);
			
			VidaRegisterResponseContainer responseContainer = new VidaRegisterResponseContainer();
			responseContainer.setRequestBody(jsonRequest);
			responseContainer.setResponseBody(jsonResponse);
			responseContainer.setResponse(gson.fromJson(jsonResponse, VidaRegisterResponse.class));
			return responseContainer;
			
		} catch (Exception e) {
			throw new VidaException(e.getLocalizedMessage(), e);
		}
		
	}

	@Override
	public AmMsuser insertRegisteredUser(VidaRegisterResponse registerResponse, UserBean userData, MsTenant tenant, RegistrationType registrationType, MsLov lovUserType, AuditContext audit) {
		
		userValidatorLogic.removeUnnecessaryRegisterParam(userData, tenant);
		String validationMessage = "";
		String hashedPhone = MssTool.getHashedString(userData.getUserPhone());
		String hashedIdNo = MssTool.getHashedString(userData.getIdNo());
		String emailService = (RegistrationType.INVITATION_SMS == registrationType) ? "1" :"0";
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(userData.getIdEmailHosting());
		
		AmMsrole role = null;
		if (null != lovUserType && GlobalVal.CODE_LOV_USER_TYPE_EMPLOYEE.equals(lovUserType.getCode())) {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_BM_MF, tenant.getTenantCode());
		} else {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(userData.getIdNo());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = userData.getUserName().split(" ");
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setFullName(StringUtils.upperCase(userData.getUserName()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword("newInv");
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("1");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		} else {
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setHashedPhone(hashedPhone);
			user.setPassword("newInv");
			user.setIsActive("1");
			user.setIsDormant("0");
			user.setChangePwdLogin("1");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUserNewTran(user);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_VIDA);
		byte[] phoneBytea = personalDataEncLogic.encryptFromString(userData.getUserPhone());
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_VIDA_CERTIFICATE_EXPIRE_TIME);
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new Object[] {"Duration Certificate VIDA"}, audit);
		commonValidatorLogic.validateNotNull(gs, validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
		int certificateDurationVendor ;
		try {
			certificateDurationVendor = Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			throw new CommonException("Invalid value Certificate Duration", ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		
		Date activatedDate = new Date();
		Date expiredDate = DateUtils.addDays(activatedDate, certificateDurationVendor);
		if (null == vendorUser) {
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive("0");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_VIDA));
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(registerResponse.getData().getEventId());
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		} else {
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive("0");
			vendorUser.setUsrUpd(audit.getCallerId());
			vendorUser.setDtmUpd(new Date());
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(registerResponse.getData().getEventId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);
		}
		
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto(), true);
			byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto(), true);
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(userData.getIdNo());
			personalDataBean.setPhoneRaw(userData.getUserPhone());
			personalDataBean.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);

		} else {

			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto());
			byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());

			AmUserPersonalData userPersonalData = personalData.getUserPersonalData();
			userPersonalData.setUsrUpd(audit.getCallerId());
			userPersonalData.setDtmUpd(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);

			personalData.setUserPersonalData(userPersonalData);
			personalData.setSelfPhotoRaw(selfiePhoto);
			personalData.setPhotoIdRaw(idPhoto);
			personalData.setIdNoRaw(userData.getIdNo());
			personalData.setPhoneRaw(userData.getUserPhone());
			personalData.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().updateUserPersonalDataNewTrans(personalData);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
		
		return user;
	}

	@Override
	public boolean isRegisterLivenessFailed(VidaRegisterResponse registerResponse, AuditContext audit) {
		for (VidaFieldBean field : registerResponse.getData().getFields()) {
			if ("liveness".equals(field.getField()) && (field.getScore() > LIVENESS_THRESHOLD || field.getScore() < 0)) {
				return true;
			}
		} 
		return false;
	}
	
	// Unused method di bawah tidak dihapus. In case, diperlukan lagi kalau Dukcapil berubah lagi.
	private int getFailedVerificationFieldCount(List<VidaFieldBean> fields) {
		int failCount = 0;
		
		for (VidaFieldBean vidaFieldBean : fields) {
			// fields that must have a score of 10
			if ((FIELD_POB.equals(vidaFieldBean.getField())
					|| FIELD_FULL_NAME.equals(vidaFieldBean.getField())
					|| FIELD_DOB.equals(vidaFieldBean.getField())
					|| FIELD_ADDRESS.equals(vidaFieldBean.getField())
					|| FIELD_PROVINCE.equals(vidaFieldBean.getField())
					|| FIELD_CITY.equals(vidaFieldBean.getField())
					|| FIELD_DISTRICT.equals(vidaFieldBean.getField())
					|| FIELD_FAMILY_CARD_NO.equals(vidaFieldBean.getField())
					|| FIELD_VILLAGE.equals(vidaFieldBean.getField())
					|| FIELD_MOTHER_MAIDEN_NAME.equals(vidaFieldBean.getField())
					|| FIELD_NIK.equals(vidaFieldBean.getField())
					|| FIELD_SELFIE.equals(vidaFieldBean.getField()))
					&& !VERIF_RESULT_TRUE.equals(vidaFieldBean.getStatus())) {
						failCount++;
			}
			
		}
		
		return failCount;
	}
	
	private boolean isVerificationFailed(VidaFieldBean vidaFieldBean) {
		
		return !VERIF_RESULT_TRUE.equals(vidaFieldBean.getStatus())
				&& (FIELD_POB.equals(vidaFieldBean.getField())
						|| FIELD_FULL_NAME.equals(vidaFieldBean.getField())
						|| FIELD_DOB.equals(vidaFieldBean.getField())
						|| FIELD_ADDRESS.equals(vidaFieldBean.getField())
						|| FIELD_PROVINCE.equals(vidaFieldBean.getField())
						|| FIELD_CITY.equals(vidaFieldBean.getField())
						|| FIELD_DISTRICT.equals(vidaFieldBean.getField())
						|| FIELD_FAMILY_CARD_NO.equals(vidaFieldBean.getField())
						|| FIELD_VILLAGE.equals(vidaFieldBean.getField())
						|| FIELD_MOTHER_MAIDEN_NAME.equals(vidaFieldBean.getField())
						|| FIELD_NIK.equals(vidaFieldBean.getField())
						|| FIELD_SELFIE.equals(vidaFieldBean.getField()));
	}
	
	// Unused method di bawah tidak dihapus. In case, diperlukan lagi kalau Dukcapil berubah lagi.
	private void appendSeperator(StringBuilder messageBuilder, int currentFailCount, int totalFail) {
		if (currentFailCount < totalFail && totalFail != 2) {
			messageBuilder.append(",");
		}
		if (currentFailCount < totalFail) {
			messageBuilder.append(" ");
		}
		if (currentFailCount == totalFail - 1) {
			messageBuilder.append("dan ");
		}
	}
	
	// Unused method di bawah tidak dihapus. In case, diperlukan lagi kalau Dukcapil berubah lagi.
	private String getFieldLabel(VidaFieldBean field) {
		if (null == field || StringUtils.isBlank(field.getField())) {
			return StringUtils.EMPTY;
		}
		
		switch (field.getField()) {
			case FIELD_POB:
				return "Tempat Lahir";
			case FIELD_FULL_NAME:
				return "Nama Lengkap";
			case FIELD_DOB:
				return "Tanggal Lahir";
			case FIELD_ADDRESS:
				return "Alamat Lengkap";
			case FIELD_PROVINCE:
				return "Provinsi";
			case FIELD_CITY:
				return "Kota";
			case FIELD_DISTRICT:
				return "Kecamatan";
			case FIELD_FAMILY_CARD_NO:
				return "No KK";
			case FIELD_VILLAGE:
				return "Kelurahan";
			case FIELD_MOTHER_MAIDEN_NAME:
				return "Nama Ibu";
			case FIELD_SELFIE:
				return "Foto Diri";
			case FIELD_NIK:
				return "NIK";
			default:
				return StringUtils.EMPTY;
		}
		
	}

	@Override
	public String getFailedRegisterMessage(VidaRegisterResponse registerResponse, AuditContext audit) {
		
		// Tolong comment code di bawah tidak dihapus. In case, diperlukan lagi kalau Dukcapil berubah lagi.
		
//		StringBuilder message = new StringBuilder();
//		int currentFailCount = 0;
//		int totalFail = getFailedVerificationFieldCount(registerResponse.getData().getFields());
//		
//		message.append("Verifikasi user gagal. ");
//		for (VidaFieldBean field : registerResponse.getData().getFields()) {
//			if (!isVerificationFailed(field)) {
//				continue;
//			}
//			
//			message.append(getFieldLabel(field));
//			currentFailCount++;
//			appendSeperator(message, currentFailCount, totalFail);
//		}
//		message.append(" tidak sesuai.");
//		return message.toString();
		
		return "Verifikasi Gagal. Nama, Tanggal Lahir, atau Foto Diri tidak sesuai. Harap cek kembali Nama dan Tanggal Lahir Anda serta mengambil ulang Foto Diri.";
	}

	@Override
	public String getErrorRegisterMessage(VidaRegisterResponse registerResponse, AuditContext audit) {
		if (CollectionUtils.isEmpty(registerResponse.getErrors())) {
			return StringUtils.EMPTY;
		}
		
		StringBuilder message = new StringBuilder();
		for (int i = 0; i < registerResponse.getErrors().size(); i++) {
			message.append(registerResponse.getErrors().get(i).getTitle());
			if (StringUtils.isNotBlank(registerResponse.getErrors().get(i).getDetail())) {
				message.append(" (");
				message.append(registerResponse.getErrors().get(i).getDetail());
				message.append(")");
			}
			if (i != registerResponse.getErrors().size() - 1) {
				message.append(" ");
			}
		} 
		return message.toString();
	}

	@Override
	public PoaResponse doPoa(PoaRequest request, MsTenant tenant, TrDocumentD docD, TrDocumentH docH, TrDocumentDSign docDSign, AuditContext audit) throws IOException, InterruptedException {
		PoaResponse toReturn = new PoaResponse();
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_VIDA);
		AmMsuser user = userValidatorLogic.validateGetUserByEmailAndVendorCode(request.getSigner().getEmail(), true, GlobalVal.VENDOR_CODE_VIDA, audit);
		
		AmGeneralsetting iterationGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_VIDA_POA_ITERATION);
		int iteration = Integer.parseInt(iterationGs.getGsValue());
		AmGeneralsetting waitTimeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_API_VIDA_POA_WAIT_TIME);
		long waitTime = Long.parseLong(waitTimeGs.getGsValue());
		
		String token = generateTokenPoa(tenant, audit);
		String trxNo = insertBalMut(docD, docH, tenant, vendor, user, request.getSigner().getEmail(), audit);
		request.setPartnerTrxId(trxNo);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBearerToken(token));
		mapHeader.add(HttpHeaders.CONTENT_TYPE, javax.ws.rs.core.MediaType.APPLICATION_JSON);
		
		WebClient client = WebClient.create(poaUri).headers(mapHeader);
		
		int code = 0;
		int currentIter = 0;
		String url = "";
		while ((toReturn.getData() == null || toReturn.getData().getId() == null) && currentIter != iteration) {
			// Autosign
			String jsonRequest = gson.toJson(request);
			for(PoaSigningInfoBean si : request.getSigningInfo()) {
				si.setPdfFile("{{base64File}}");
			}
			String logRequest = gson.toJson(request);
			LOG.info("Request PoA : {}", logRequest);
			LOG.info("Header PoA : {}", mapHeader);
			LOG.info("PoA URL : {}", poaUri);
			
			Date start = new Date();
			Response response = client.post(jsonRequest);
			Date end = new Date();
			logProcessDuration("VIDA PoA", start, end);
			
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String resultPoa =  IOUtils.toString(isReader);
			LOG.info("Result PoA : {}", resultPoa);
			
			toReturn = gson.fromJson(resultPoa, PoaResponse.class);
			if (toReturn.getErrors() != null) {
				currentIter++;
				LOG.info(CONST_LOG_POAPAUSED);
				TimeUnit.MILLISECONDS.sleep(waitTime);
				continue;
			}
			
			//Status Check
			PoaStatusCheckResponse statusCheck = this.poaStatusCheck(toReturn.getData().getId(), mapHeader);
			code = statusCheck.getData().getCode();
			LOG.info("PoA status : {}", code);
			
			if (code != 2 && toReturn.getData().getId() != null) {
				int checkStatusIter =  0;
				while(code != 2 && checkStatusIter != iteration) {
					LOG.info(CONST_LOG_POAPAUSED);
					TimeUnit.MILLISECONDS.sleep(500);
					statusCheck = this.poaStatusCheck(toReturn.getData().getId(), mapHeader);
					code = statusCheck.getData().getCode();
					LOG.info("PoA status : {}", code);
					checkStatusIter++;
				}
			}
			
			currentIter++;
			if (toReturn.getData() == null || toReturn.getData().getClass() == null) {
				LOG.info(CONST_LOG_POAPAUSED);
				TimeUnit.MILLISECONDS.sleep(waitTime);
			} else if (code == 2) {
				toReturn.setSignedDocument(getPoaSuccessDocument(statusCheck.getData().getSignedDocs().isEmpty() ? null : statusCheck.getData().getSignedDocs().get(0).getDocumentUrl()));
				url = statusCheck.getData().getSignedDocs().isEmpty() ? null : statusCheck.getData().getSignedDocs().get(0).getDocumentUrl();
			}
		}
		
		if (code == 2 && StringUtils.isNotBlank(url)) {
			toReturn.setSignedDocument(getPoaSuccessDocument(url));
		} else {
			PoaErrorBean[] vidaErrors = toReturn.getErrors();
			PoaErrorBean[] errors = new PoaErrorBean[1];
			PoaErrorBean bean = new PoaErrorBean();
			bean.setCode(9999);
			String title = StringUtils.isBlank(url) && 2 != code ? "PoA waiting time exceeded." : "Fail processing.";
			if (null != vidaErrors && vidaErrors[0].getTitle().contains("CVV")) {
				title = "Invalid Autosign Key.";
			} else if (null != vidaErrors && vidaErrors[0].getTitle().contains("Invalid")){
				title = "PoA Id Not Match.";
			}
			
			bean.setTitle(title);
			errors[0] = bean;
			toReturn.setErrors(errors);
		}
		
		toReturn.setCode(code);
		
		if (toReturn.getErrors() == null) {
			docD.setTotalSigned((short) (docD.getTotalSigned() + 1));
			docD.setUsrUpd(audit.getCallerId());
			docD.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetail(docD);
			
			docDSign.setSignDate(new Date());
			docDSign.setUsrUpd(audit.getCallerId());
			docDSign.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDSign(docDSign);
		}
		
		//update balance
		if (toReturn.getErrors() == null) {
			TrBalanceMutation bm = daoFactory.getBalanceMutationDao().getBalanceMutationByTrxNo(trxNo);
			bm.setQty(-1);
			bm.setVendorTrxNo(toReturn.getData().getId());
			daoFactory.getBalanceMutationDao().updateTrBalanceMutation(bm);
		}
		
		toReturn.setTrxNo(trxNo);
		
		return toReturn;
	}
	
	private String getPoaSuccessDocument(String url) throws IOException {
		if (StringUtils.isBlank(url)) {
			return null;
		}
		
		LOG.info("Getting PoA Document.");
		WebClient client = WebClient.create(url);
		Date start = new Date();
		Response response = client.get();
		Date end = new Date();
		logProcessDuration("Get PoA Document", start, end);
		
		InputStream is = (InputStream) response.getEntity();
		byte[] pdfBytes = org.apache.commons.io.IOUtils.toByteArray(is);
		return Base64.getEncoder().encodeToString(pdfBytes);
	}
	
	private String insertBalMut(TrDocumentD docD, TrDocumentH docH, MsTenant tenant, MsVendor vendor, AmMsuser user, String email, AuditContext audit) {
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		Date trxDate = new Date();
		String notes = "Auto Sign (" + email + ")";
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USGN);
		
		saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendor, trxDate, docH.getRefNumber(), 0, trxNo, user, notes, null, docH.getMsOffice(), docH.getMsBusinessLine(), audit);
		
		return trxNo;
	}

	@Override
	public PoaRequest buildPoaRequest(String email, String idNo, String pdfFile, String signLoc, int page, boolean qrEnable, String ipAddr, TrDocumentDSign docDSign, MsTenant tenant, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, UnsupportedEncodingException {
		PoaRequest poaReq = new PoaRequest();
		PoaSignerBean signer = new PoaSignerBean();
		PoaSigningInfoBean signingInfo = new PoaSigningInfoBean();
		PoaSigningInfoBean[] arrSignInfo = new PoaSigningInfoBean[1];
		PoaSigningInfoAppearanceBean appearance = new PoaSigningInfoAppearanceBean();

		MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GENERALSETTING_API_KEY_VIDA_CVV);
		commonValidatorLogic.validateNotNull(ts, getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_NOT_FOUND, new Object[] {AmGlobalKey.GENERALSETTING_API_KEY_VIDA_CVV, tenant.getTenantCode()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		commonValidatorLogic.validateNotNull(ts.getSettingValue(), getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_VALUE_EMPTY, new Object[] {AmGlobalKey.GENERALSETTING_API_KEY_VIDA_CVV}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailAndVendorCode(email, false, GlobalVal.VENDOR_CODE_VIDA, audit);
		MsVendorRegisteredUser vru = null;
		if (user != null) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), GlobalVal.VENDOR_CODE_VIDA);
		}
		
		if (user == null || vru == null || !"1".equals(vru.getIsActive()) || StringUtils.isBlank(vru.getVendorUserAutosignKey())) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.usercannotdoautosign", new Object[] {idNo}, this.retrieveLocaleAudit(audit)),
					ReasonDocument.AUTOSIGN_FAILED);
		} else {
			signer.setEmail(vru.getSignerRegisteredEmail());
			signer.setApiKey(ts.getSettingValue());
			signer.setKeyId(vru.getPoaId());
			try {
				signer.setEncCVV(cvvEncryption(vru, tenant, audit));
			} catch (InvalidKeyException e) {
				throw new DocumentException("Fail PoA Vida : Invalid Autosign Key.", ReasonDocument.AUTOSIGN_FAILED);
			}
			
			poaReq.setSigner(signer);
			
			MsTenantSettings cs = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_POA_CUSTOM_SIGN_STATUS);

			if ("1".equals(cs.getSettingValue())) {
				byte[] signImage = cloudStorageLogic.getCustomSign(tenant.getTenantCode(), idNo);
				if (null == signImage) {
					throw new DocumentException(messageSource.getMessage("businesslogic.document.customsignnotexist", new Object[] {idNo}, this.retrieveLocaleAudit(audit)), ReasonDocument.CUSTOM_SIGN_NOT_EXIST);
				} else {
			        appearance.setType("provided");
			        appearance.setSignImage(Base64.getEncoder().encodeToString(signImage));
				}
			} else {
				appearance.setType("standard");
			}
			signingInfo.setAppearance(appearance);
			
			SignatureDetailBean detailBean = gson.fromJson(signLoc, SignatureDetailBean.class);
			signingInfo.setHeight(String.valueOf((int) detailBean.getH()));
			signingInfo.setPageNo(String.valueOf(page));
			signingInfo.setPdfFile(pdfFile);
			signingInfo.setWidth(String.valueOf((int) detailBean.getW()));
			signingInfo.setxPoint(String.valueOf((int) detailBean.getX()));
			signingInfo.setyPoint(String.valueOf((int) detailBean.getY()));
			signingInfo.setQrEnable(qrEnable);
			arrSignInfo[0] = signingInfo;
			poaReq.setSigningInfo(arrSignInfo);
			
			RequestInfoBean requestInfo = new RequestInfoBean();
			requestInfo.setSrcIp(ipAddr);
			requestInfo.setUserAgent("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36");
			requestInfo.setConsentTimestamp(String.valueOf(new Date().getTime()));
			poaReq.setRequestInfo(requestInfo);
		}
		
		return poaReq;
	}

	@Override
	public PoaStatusCheckResponse poaStatusCheck(String transactionId, MultivaluedMap<String, String> mapHeader) throws IOException {
		LOG.info("PoA Check Status start.");
		String fixUri = poaCheckstatusUri + transactionId;
		WebClient client = WebClient.create(fixUri).headers(mapHeader);
		
		Date start = new Date();
		Response response = client.get();
		Date end = new Date();
		logProcessDuration("PoA Check Status", start, end);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String resultPoaStatusCheck =  IOUtils.toString(isReader);
		LOG.info("PoA Status check Result : {}", resultPoaStatusCheck);
		return gson.fromJson(resultPoaStatusCheck, PoaStatusCheckResponse.class);
	}

	@Override
	public VidaRegisterResponseContainer registerExternal(RegisterExternalRequest request, MsTenant tenant, Date consentTime, String reservedTrxNo, AuditContext audit) {
		try {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_VIDA);
			String token = generateToken(vendor, tenant);
			
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, "Bearer " + token);
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			LOG.info("Vida register request header: {}", mapHeader);
			WebClient client = WebClient.create(registerUri).headers(mapHeader);
			
			// Prepare body
			VidaRegisterRequest registerRequest = buildExternalVidaRegisterRequest(request, consentTime, reservedTrxNo);
			String jsonRequest = gson.toJson(registerRequest);
			LOG.info("Vida register request: {}", jsonRequest);
						
			// Get response
			Date startTime = new Date();
			Response response = client.post(jsonRequest);
			Date finishTime = new Date();
			logProcessDuration("Vida register", startTime, finishTime);
			LOG.info("Vida register response code: {} {}", response.getStatus(), response.getStatusInfo().getReasonPhrase());
						
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String jsonResponse = IOUtils.toString(isReader);
			LOG.info("Vida register response: {}", jsonResponse);
			
			VidaRegisterResponseContainer responseContainer = new VidaRegisterResponseContainer();
			responseContainer.setRequestBody(jsonRequest);
			responseContainer.setResponseBody(jsonResponse);
			responseContainer.setResponse(gson.fromJson(jsonResponse, VidaRegisterResponse.class));
			return responseContainer;
		} catch (Exception e) {
			throw new VidaException(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public AmMsuser insertExternalRegisteredUser(VidaRegisterResponse registerResponse, RegisterExternalRequest request, MsTenant tenant, AuditContext audit) {
		
		userValidatorLogic.removeUnnecessaryRegisterExternalParam(request, tenant);
		boolean doesNotNeedPassword = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_ACTIVATION);
		String validationMessage = "";
		String hashedPhone = MssTool.getHashedString(request.getTlp());
		String hashedIdNo = MssTool.getHashedString(request.getIdKtp());
		String emailService = (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) ? "1" :"0";
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(request.getIdEmailHosting());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(request.getIdKtp());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = request.getNama().split(" ");
			String hashedPassword = null;
			if (doesNotNeedPassword) {
				hashedPassword = "noPassword";
			} else {
				hashedPassword = PasswordHash.createHash(request.getPassword());
			}
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setFullName(StringUtils.upperCase(request.getNama()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword(hashedPassword);
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("0");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		} else {
			if (StringUtils.isNotBlank(request.getPassword())) {
				String hashedPassword = PasswordHash.createHash(request.getPassword());
				user.setPassword(hashedPassword);
			}
			user.setIsActive("1");
			user.setIsDormant("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setHashedPhone(hashedPhone);
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUserNewTran(user);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_VIDA);
		byte[] phoneBytea = personalDataEncLogic.encryptFromString(request.getTlp());
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_VIDA_CERTIFICATE_EXPIRE_TIME);
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new Object[] {"Duration Certificate VIDA"}, audit);
		commonValidatorLogic.validateNotNull(gs, validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
		int certificateDurationVendor ;
		try {
			certificateDurationVendor = Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			throw new CommonException("Invalid value Certificate Duration", ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		Date activatedDate = new Date();
		Date expiredDate = DateUtils.addDays(activatedDate, certificateDurationVendor);
		
		if (null == vendorUser) {
			
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_VIDA));
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(registerResponse.getData().getEventId());
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		} else {
			
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrUpd(audit.getCallerId());
			vendorUser.setDtmUpd(new Date());
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setVendorRegistrationId(registerResponse.getData().getEventId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);
		}
		
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(request.getTglLahir(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = StringUtils.isBlank(request.getSelfPhoto()) ? null : MssTool.imageStringToByteArray(request.getSelfPhoto());
			byte[] idPhoto = StringUtils.isBlank(request.getIdPhoto()) ? null : MssTool.imageStringToByteArray(request.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(request.getProvinsi());
			zipcodeCityBean.setKota(request.getKota());
			zipcodeCityBean.setKecamatan(request.getKecamatan());
			zipcodeCityBean.setKelurahan(request.getKelurahan());
			zipcodeCityBean.setZipcode(request.getKodePos());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(request.getJenisKelamin()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(request.getTmpLahir()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(request.getIdKtp());
			personalDataBean.setPhoneRaw(request.getTlp());
			personalDataBean.setAddressRaw(StringUtils.upperCase(request.getAlamat()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
		
		return user;
	}

	@Override
	public RegisterVerificationStatusBean buildVerificationStatusBean(VidaRegisterResponse registerResponse, AuditContext audit) {
		
		if (null == registerResponse.getData() || null == registerResponse.getData().getFields()) {
			return new RegisterVerificationStatusBean();
		}
		
		boolean isLivenessFailed = isRegisterLivenessFailed(registerResponse, audit);
		
		RegisterVerificationStatusBean verifStatus = new RegisterVerificationStatusBean();
		verifStatus.setLiveness(isLivenessFailed ? GlobalVal.CONST_FALSE : GlobalVal.CONST_TRUE);
		
		for (VidaFieldBean field : registerResponse.getData().getFields()) {
			
			boolean isVerifFailed = isVerificationFailed(field);
			
			switch (field.getField()) {
				case FIELD_NIK:
					verifStatus.setNik(isVerifFailed ? GlobalVal.NIK_UNREGISTERED : GlobalVal.NIK_REGISTERED);
					break;
				case FIELD_FULL_NAME:
					verifStatus.setName(isVerifFailed ? GlobalVal.CONST_FALSE : GlobalVal.CONST_TRUE);
					break;
				case FIELD_DOB:
					verifStatus.setBirthDate(isVerifFailed ? GlobalVal.CONST_FALSE : GlobalVal.CONST_TRUE);
					break;
				case FIELD_SELFIE:
					verifStatus.setSelfieCheck(isVerifFailed ? GlobalVal.CONST_FALSE : GlobalVal.CONST_TRUE);
					break;
				default:
					break;
			}
		}
		
		return verifStatus;
	}

	@Override
	public Date getConsentTime() {
		AmGeneralsetting genset = daoFactory.getGeneralSettingDao().getGsObjByCode("VIDA_CONSENT_TIME_ALTERATION");
		if (null == genset || StringUtils.isBlank(genset.getGsValue())) {
			return new Date();
		}
		
		try {
			Integer minutesAlteration = Integer.parseInt(genset.getGsValue());
			return DateUtils.addMinutes(new Date(), minutesAlteration);
		} catch (Exception e) {
			LOG.error("Failed to alter VIDA consent time", e);
			return new Date();
		}
	}
}
