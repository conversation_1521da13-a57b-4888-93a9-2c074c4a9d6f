package com.adins.esign.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.UserActivityLogDao;
import com.adins.esign.model.TrUserActivityLog;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class UserActivityLogDaoHbn extends BaseDaoHbn implements UserActivityLogDao {

	@Override
	public void insertUserActivityLog(TrUserActivityLog userActivityLog) {
		userActivityLog.setUsrCrt(MssTool.maskData(userActivityLog.getUsrCrt()));
		this.managerDAO.insert(userActivityLog);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserActivityLogNewTrx(TrUserActivityLog userActivityLog) {
		userActivityLog.setUsrCrt(MssTool.maskData(userActivityLog.getUsrCrt()));
		this.managerDAO.insert(userActivityLog);
	}

	@Override
	public TrUserActivityLog getLastLoginActivityByLoginId(String loginId) {
		if (StringUtils.isBlank(loginId)) {
			return null;
		}

		return this.managerDAO.selectOne(
				"from TrUserActivityLog ual "
				+ "join fetch ual.idMsuser u "
				+ "join fetch ual.idMsTenant t "
				+ "join fetch ual.idMsRole r "
				+ "join fetch ual.lovUserActivityLog lov "
				+ "where u.loginId = :loginId "
				+ "and lov.lovGroup = :lovGroup "
				+ "and lov.code = :lovCode "
				+ "order by ual.dtmCrt desc",
				new Object[][] {
					{"loginId", StringUtils.upperCase(loginId)},
					{"lovGroup", GlobalVal.LOV_GROUP_ACTIVITY_LOG_TYPE},
					{"lovCode", GlobalVal.CODE_LOV_ACTIVITY_LOG_TYPE_LOGIN}
				});
	}
}
