package com.adins.esign.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.adins.am.model.custom.CreateableEntity;

@Entity
@Table(name = "tr_background_process_fail")
public class TrBackgroundProcessFail extends CreateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	
	private long idBackgroundProcessFail;
	private String processName;
	private String processLocation;
	private String stackTrace;
	private String processStatus;
	
	public TrBackgroundProcessFail() {
		
	}
	
	public TrBackgroundProcessFail(long idBackgroundProcessFail, String processName, String processLocation,
			String stackTrace, String processStatus, String usrCrt, Date dtmCrt) {
		super();
		this.idBackgroundProcessFail = idBackgroundProcessFail;
		this.processName = processName;
		this.processLocation = processLocation;
		this.stackTrace = stackTrace;
		this.processStatus = processStatus;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}
	
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_background_process_fail", unique = true, nullable = false)
	public long getIdBackgroundProcessFail() {
		return idBackgroundProcessFail;
	}
	public void setIdBackgroundProcessFail(long idBackgroundProcessFail) {
		this.idBackgroundProcessFail = idBackgroundProcessFail;
	}
	
	@Column(name = "process_name", length = 50)
	public String getProcessName() {
		return processName;
	}
	public void setProcessName(String processName) {
		this.processName = processName;
	}
	
	@Column(name = "process_location", length = 50)
	public String getProcessLocation() {
		return processLocation;
	}
	public void setProcessLocation(String processLocation) {
		this.processLocation = processLocation;
	}
	
	@Column(name = "stack_trace")
	public String getStackTrace() {
		return stackTrace;
	}
	public void setStackTrace(String stackTrace) {
		this.stackTrace = stackTrace;
	}
	
	@Column(name = "process_status", length = 1)
	public String getProcessStatus() {
		return processStatus;
	}
	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}
	
	
	
}
