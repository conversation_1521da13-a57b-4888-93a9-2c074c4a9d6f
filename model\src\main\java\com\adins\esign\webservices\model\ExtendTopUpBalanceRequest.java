package com.adins.esign.webservices.model;

import com.adins.esign.annotations.Required;
import com.adins.esign.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class ExtendTopUpBalanceRequest extends MssRequestType{

	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Id Balance Mutation")
	@Required(allowBlankString = false)
	private String idBalanceMutation;
		
	@Required(allowBlankString = false)
	private String topupDate;
	
	@Required(allowBlankString = false)
	private String tenantCode;
	
	@Required(allowBlankString = false)
	private String vendorCode;
	
	@Required(allowBlankString = false)
	private String extendDate;
	
	public String getIdBalanceMutation() {
		return idBalanceMutation;
	}
	public void setIdBalanceMutation(String idBalanceMutation) {
		this.idBalanceMutation = idBalanceMutation;
	}
	public String getTopupDate() {
		return topupDate;
	}
	public void setTopupDate(String topupDate) {
		this.topupDate = topupDate;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getVendorCode() {
		return vendorCode;
	}
	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	public String getExtendDate() {
		return extendDate;
	}
	public void setExtendDate(String extendDate) {
		this.extendDate = extendDate;
	}
	
}
