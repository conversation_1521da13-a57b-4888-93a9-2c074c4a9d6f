package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;


public class UserException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonUser {
		LOGIN_ID_NOT_UNIQUE,
		LOGIN_ID_NOT_EXISTS,
		LOGI<PERSON>_ID_EMPTY,
		ERROR_EXIST,
		PARAM_INVALID,
		ALREADY_REGISTERED,
		OTP_REGISTER_EMAIL_ERROR,
		OTP_EMPTY,
		OTP_INVALID,
		OTP_EXPIRED,
		INACTIVE_USER,
		INVALID_USER_ROLE,
		PERSONAL_DATA_NOT_FOUND,
		REQUEST_TYPE_EMPTY,
		REQUEST_TYPE_INVALID,
		SEND_USER_INFO_EMAIL_ERROR,
		USER_NOT_FOUND,
		TENANT_NOT_FOUND,
		VENDOR_NOT_FOUND,
		ROLE_NOT_FOUND,
		PHONE_NO_ALREADY_EXIST,
		PHONE_NO_EMPTY,
		NIK_ALREADY_EXIST,
		NIK_EMPTY,
		EMAIL_EMPTY,
		ENCRYPT_USER_DATA_ALGORITHM_NOT_FOUND,
		INVALID_NIK_LENGTH,
		PASSWORD_CANNOT_BE_EMPTY,
		FULLNAME_EMPTY,
		EMAIL_OR_PHONE_EMPTY,
		IS_VERIFY_EMAIL_EMPTY,
		VERIFY_NOT_MATCH,
		NOT_REGISTERED,
		INVALID_GENDER_CODE,
		INVALID_UPDATE_CODE,
		USER_TENANT_NOT_FOUND,
		GENERATE_IL_REPORT_ERROR,
		PARSE_DATE_FILTER_ERROR,
		UNREGISTERED_PHONE,
		UNREGISTERED_EMAIL,
		UNREGISTERED_NIK,
		UNKNOWN,
		ROLE_CODE_NOT_FOUND,
		IS_USER_MANAGEMENT_INVALID,
		ROLE_CODE_NOT_AVAILABLE_IN_TENANT,
		INVALID_TENANT_AND_CALLER,
		INVALID_ROLE_AND_USER_AND_TENANT,
		USER_NOT_FOUND_WITH_THAT_EMAIL,
		USER_NOT_REGISTERED_IN_VENDOR,
		SIGNER_EMAIL_EMPTY,
		VENDOR_CODE_EMPTY,
		TENANT_CODE_EMPTY,
		PASSWORD_NOT_MATCH,
		MAX_OTP_ACTIVATION_USER_REACHED,
		USER_HAS_NOT_REGISTERED,
		USER_ALREADY_ACTIVATED,
		USER_NOT_FOUND_WITH_THAT_PHONE_NO,
		PHONE_NOT_MATCH_WITH_INVITATION,
		USER_HAS_NOT_ACTIVATED,
		USER_NOT_REGISTERED_IN_TENANT,
		USER_DOES_NOT_HAVE_ANY_DOCUMENT,
		MAX_LIVENESS_FACECOMPARE_REACHED,
		PHOTO_NOT_FOUND,
		INVALID_FORMAT,
		NIK_NOT_NUMBER,
		USER_WITH_EMAIL_NOT_REGISTERED_WITH_PHONE,
		USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL,
		MAX_RESET_PASSWORD_REACHED,
		SEND_EMAIL_CERTIFICATE_NOTIFICATION_ERROR,
		SEND_SMS_CERTIFICATE_NOTIFICATION_ERROR,
		UNHANDLED_VENDOR,
		NOT_NEED_ACTIVATE_VENDOR,
		URL_FORWARDER_CODE_INVALID,
		URL_FORWARDER_EXPIRED,
		INVITATION_NOT_RESENT,
		RESET_CODE_EMPTY,
		OTP_SENDING_MEDIA_NOT_VALID,
		USER_REGISTERED_WITHOUT_EMAIL,
		USER_CERTIFICATE_EXPIRED
	}

	private final ReasonUser reason;

	public UserException(ReasonUser reason) {
		this.reason = reason;
	}

	public UserException(String message, ReasonUser reason) {
		super(message);
		this.reason = reason;
	}

	public UserException(Throwable ex, ReasonUser reason) {
		super(ex);
		this.reason = reason;
	}

	public UserException(String message, Throwable ex, ReasonUser reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonUser getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case ERROR_EXIST:
				return StatusCode.ERROR_EXIST;
			case LOGIN_ID_NOT_UNIQUE:
				return StatusCode.LOGIN_ID_NOT_UNIQUE;
			case LOGIN_ID_NOT_EXISTS:
				return StatusCode.LOGIN_ID_NOT_EXISTS;
			case LOGIN_ID_EMPTY:
				return StatusCode.LOGIN_ID_EMPTY;
			case PARAM_INVALID:
				return StatusCode.INVALID_IDENTIFIER;
			case ALREADY_REGISTERED:
				return StatusCode.USER_ALREADY_REGISTERED;
			case OTP_REGISTER_EMAIL_ERROR:
				return StatusCode.OTP_REGISTER_EMAIL_ERROR;
			case OTP_EMPTY:
				return StatusCode.OTP_EMPTY;
			case OTP_EXPIRED:
				return StatusCode.OTP_EXPIRED;	
			case OTP_INVALID:
				return StatusCode.OTP_INVALID;
			case INACTIVE_USER:
				return StatusCode.INACTIVE_USER;
			case INVALID_USER_ROLE:
				return StatusCode.USER_ROLE_INVALID;
			case PERSONAL_DATA_NOT_FOUND:
				return StatusCode.PERSONAL_DATA_NOT_FOUND;
			case REQUEST_TYPE_EMPTY:
				return StatusCode.INQ_USER_TYPE_EMPTY;
			case REQUEST_TYPE_INVALID:
				return StatusCode.INQ_USER_TYPE_INVALID;
			case SEND_USER_INFO_EMAIL_ERROR:
				return StatusCode.SEND_USER_INFO_EMAIL_ERROR;
			case TENANT_NOT_FOUND:
				return StatusCode.TENANT_NOT_EXISTS;
			case VENDOR_NOT_FOUND:
				return StatusCode.VENDOR_NOT_EXISTS;
			case PHONE_NO_ALREADY_EXIST:
				return StatusCode.PHONE_NUM_ALREADY_EXIST;
			case ENCRYPT_USER_DATA_ALGORITHM_NOT_FOUND:
				return StatusCode.ALGORITHM_NOT_FOUND;
			case INVALID_NIK_LENGTH:
				return StatusCode.INVALID_NIK_LENGTH;
			case NIK_ALREADY_EXIST:
				return StatusCode.USER_NIK_ALREADY_EXISTED;
			case PASSWORD_CANNOT_BE_EMPTY:
				return StatusCode.PASSWORD_CANNOT_BE_EMPTY;
			case PHONE_NO_EMPTY:
				return StatusCode.PHONE_NUM_EMPTY;
			case NIK_EMPTY:
				return StatusCode.USER_NIK_EMPTY;
			case EMAIL_OR_PHONE_EMPTY:
				return StatusCode.EMAIL_OR_PHONE_EMPTY;
			case IS_VERIFY_EMAIL_EMPTY:
				return StatusCode.IS_VERIFY_EMAIL_EMPTY;
			case VERIFY_NOT_MATCH:
				return StatusCode.VERIFY_NOT_MATCH;
			case NOT_REGISTERED:
				return StatusCode.NOT_REGISTERED;
			case INVALID_GENDER_CODE:
				return StatusCode.INVALID_GENDER_CODE;
			case INVALID_UPDATE_CODE:
				return StatusCode.INVALID_UPDATE_CODE;
			case USER_TENANT_NOT_FOUND:
				return StatusCode.USER_TENANT_NOT_FOUND;
			case GENERATE_IL_REPORT_ERROR:
				return StatusCode.GENERATE_IL_REPORT_ERROR;
			case UNREGISTERED_PHONE:
				return StatusCode.UNREGISTERED_PHONE;
			case UNREGISTERED_EMAIL:
				return StatusCode.UNREGISTERED_EMAIL;
			case UNREGISTERED_NIK:
				return StatusCode.UNREGISTERED_NIK;
			case USER_NOT_FOUND:
				return StatusCode.USER_NOT_FOUND;
			case ROLE_CODE_NOT_FOUND:
				return StatusCode.ROLE_CODE_NOT_FOUND;
			case IS_USER_MANAGEMENT_INVALID:
				return StatusCode.IS_USER_MANAGEMENT_INVALID;
			case ROLE_CODE_NOT_AVAILABLE_IN_TENANT:
				return StatusCode.ROLE_CODE_NOT_AVAILABLE_IN_TENANT;
			case INVALID_TENANT_AND_CALLER:
				return StatusCode.INVALID_TENANT_AND_CALLER;
			case INVALID_ROLE_AND_USER_AND_TENANT:
				return StatusCode.INVALID_ROLE_AND_USER_AND_TENANT;
			case USER_NOT_FOUND_WITH_THAT_EMAIL:
				return StatusCode.USER_NOT_FOUND_WITH_THAT_EMAIL;
			case USER_NOT_REGISTERED_IN_VENDOR:
				return StatusCode.USER_NOT_REGISTERED_IN_VENDOR;
			case SIGNER_EMAIL_EMPTY:
				return StatusCode.SIGNER_EMAIL_EMPTY;
			case VENDOR_CODE_EMPTY:
				return StatusCode.VENDOR_CODE_EMPTY;
			case TENANT_CODE_EMPTY:
				return StatusCode.TENANT_CODE_EMPTY;
			case PASSWORD_NOT_MATCH:
				return StatusCode.PASSWORD_NOT_MATCH;
			case MAX_OTP_ACTIVATION_USER_REACHED:
				return StatusCode.MAX_OTP_ACTIVATION_USER_REACHED;
			case USER_HAS_NOT_REGISTERED:
				return StatusCode.USER_HAS_NOT_REGISTERED;
			case USER_ALREADY_ACTIVATED:
				return StatusCode.USER_ALREADY_ACTIVATED;
			case USER_NOT_FOUND_WITH_THAT_PHONE_NO:
				return StatusCode.USER_NOT_FOUND_WITH_THAT_PHONE_NO;
			case PHONE_NOT_MATCH_WITH_INVITATION:
				return StatusCode.PHONE_NOT_MATCH_WITH_INVITATION;
			case USER_HAS_NOT_ACTIVATED:
				return StatusCode.USER_HAS_NOT_ACTIVATED;
			case USER_NOT_REGISTERED_IN_TENANT:
				return StatusCode.USER_NOT_REGISTERED_IN_TENANT;
			case USER_DOES_NOT_HAVE_ANY_DOCUMENT:
				return StatusCode.USER_DOES_NOT_HAVE_ANY_DOCUMENT;
			case MAX_LIVENESS_FACECOMPARE_REACHED:
				return StatusCode.MAX_LIVENESS_FACECOMPARE_REACHED;
			case PHOTO_NOT_FOUND:
				return StatusCode.PHOTO_NOT_FOUND;
			case INVALID_FORMAT:
				return StatusCode.INVALID_FORMAT;
			case EMAIL_EMPTY:
				return StatusCode.EMAIL_EMPTY;
			case NIK_NOT_NUMBER:
				return StatusCode.NIK_IS_NOT_NUMBER;
			case USER_WITH_EMAIL_NOT_REGISTERED_WITH_PHONE:
				return StatusCode.USER_WITH_EMAIL_NOT_REGISTERED_WITH_THAT_PHONE;
			case USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL:
				return StatusCode.USER_WITH_PHONE_NOT_REGISTERED_WITH_THAT_EMAIL;
			case MAX_RESET_PASSWORD_REACHED:
				return StatusCode.MAX_RESET_PASSWORD_REACHED;
			case SEND_EMAIL_CERTIFICATE_NOTIFICATION_ERROR:
				return StatusCode.SEND_EMAIL_CERTIFICATE_NOTIFICATION_ERROR;
			case SEND_SMS_CERTIFICATE_NOTIFICATION_ERROR:
				return StatusCode.SEND_SMS_CERTIFICATE_NOTIFICATION_ERROR;
			case UNHANDLED_VENDOR:
				return StatusCode.UNHANDLED_VENDOR;
			case NOT_NEED_ACTIVATE_VENDOR:
				return StatusCode.NOT_NEED_ACTIVATE_VENDOR;
			case URL_FORWARDER_CODE_INVALID:
				return StatusCode.URL_FORWORDER_CODE_INVALID;
			case URL_FORWARDER_EXPIRED: 
				return StatusCode.URL_FORWARDER_EXPIRED;
			case INVITATION_NOT_RESENT: 
				return StatusCode.INVITATION_NOT_RESENT;
			case RESET_CODE_EMPTY: 
				return StatusCode.RESET_CODE_EMPTY;
			case OTP_SENDING_MEDIA_NOT_VALID: 
				return StatusCode.OTP_SENDING_MEDIA_NOT_VALID;
			case USER_REGISTERED_WITHOUT_EMAIL: 
				return StatusCode.USER_REGISTERED_WITHOUT_EMAIL;
			case USER_CERTIFICATE_EXPIRED: 
				return StatusCode.USER_CERTIFICATE_EXPIRED;
			default:
				return StatusCode.UNKNOWN;
			}
			
		}
		return StatusCode.UNKNOWN;
	}
}
