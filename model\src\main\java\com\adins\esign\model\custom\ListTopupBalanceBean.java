package com.adins.esign.model.custom;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class ListTopupBalanceBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigInteger no;
	private BigInteger idBalanceMutation;
	private String trxNo;
	private String tenantName;
	private String vendorName;
	private String balanceName;
	private Date expiredDate;
	private Date topupDate;
	private Date lastExpiredDate;
	private Integer extendExpiredAttempt;
	private BigDecimal balancePrice;
	private String refNumber;
	private String tenantCode;
	private String vendorCode;
	private String balanceTypeCode;
	private String description;
	private String status;
	public String getStatus() {
		if(status.equals("1")) {
			return "Is Used";
		}else if(status.equals("0")) {
			return "Not Used";
		}else {
			return status;
		}
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public BigInteger getNo() {
		return no;
	}
	public void setNo(BigInteger no) {
		this.no = no;
	}
	public String getTenantName() {
		return tenantName;
	}
	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}
	public String getVendorName() {
		return vendorName;
	}
	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}
	public String getBalanceName() {
		return balanceName;
	}
	public void setBalanceName(String balanceName) {
		this.balanceName = balanceName;
	}
	public Date getTopupDate() {
		return topupDate;
	}
	public void setTopupDate(Date topupDate) {
		this.topupDate = topupDate;
	}
	public Integer getExtendExpiredAttempt() {
		return extendExpiredAttempt;
	}
	public void setExtendExpiredAttempt(Integer extendExpiredAttempt) {
		this.extendExpiredAttempt = extendExpiredAttempt;
	}
	public BigDecimal getBalancePrice() {
		return balancePrice;
	}
	public void setBalancePrice(BigDecimal balancePrice) {
		this.balancePrice = balancePrice;
	}
	public String getRefNumber() {
		return refNumber;
	}
	public void setRefNumber(String refNumber) {
		this.refNumber = refNumber;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getVendorCode() {
		return vendorCode;
	}
	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	public String getBalanceTypeCode() {
		return balanceTypeCode;
	}
	public void setBalanceTypeCode(String balanceTypeCode) {
		this.balanceTypeCode = balanceTypeCode;
	}
	public Date getExpiredDate() {
		return expiredDate;
	}
	public void setExpiredDate(Date expiredDate) {
		this.expiredDate = expiredDate;
	}
	public Date getLastExpiredDate() {
		return lastExpiredDate;
	}
	public void setLastExpiredDate(Date lastExpiredDate) {
		this.lastExpiredDate = lastExpiredDate;
	}
	public String getIdBalanceMutation() {
	    return idBalanceMutation != null ? idBalanceMutation.toString() : null;
	}
	public void setIdBalanceMutation(BigInteger idBalanceMutation) {
		this.idBalanceMutation = idBalanceMutation;
	}
	public String getTrxNo() {
		return trxNo;
	}
	public void setTrxNo(String trxNo) {
		this.trxNo = trxNo;
	}
	
	
}
