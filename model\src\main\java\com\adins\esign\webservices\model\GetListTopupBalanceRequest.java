package com.adins.esign.webservices.model;

import com.adins.framework.service.base.model.MssRequestType;

public class GetListTopupBalanceRequest extends MssRequestType {

	private static final long serialVersionUID = 1L;

	private String status;
	private String expiredDateStart;
	private String expiredDateEnd;
	private String refNumber;
	private int page;
	private String tenantCode;
	private String vendorCode;
	private String balanceTypeCode;
	private String description;
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getExpiredDateStart() {
		return expiredDateStart;
	}
	public void setExpiredDateStart(String expiredDateStart) {
		this.expiredDateStart = expiredDateStart;
	}
	public String getExpiredDateEnd() {
		return expiredDateEnd;
	}
	public void setExpiredDateEnd(String expiredDateEnd) {
		this.expiredDateEnd = expiredDateEnd;
	}
	public String getRefNumber() {
		return refNumber;
	}
	public void setRefNumber(String refNumber) {
		this.refNumber = refNumber;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getVendorCode() {
		return vendorCode;
	}
	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	public String getBalanceTypeCode() {
		return balanceTypeCode;
	}
	public void setBalanceTypeCode(String balanceTypeCode) {
		this.balanceTypeCode = balanceTypeCode;
	}
	
	
}
