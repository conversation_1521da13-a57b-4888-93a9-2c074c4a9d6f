package com.adins.esign.webservices.model;

import java.util.List;

import com.adins.esign.model.custom.ListTopupBalanceBean;
import com.adins.framework.service.base.model.MssResponseType;

public class GetListTopupBalanceResponse extends MssResponseType {

	private static final long serialVersionUID = 1L;
	
	private int page;
	private int totalPage;
	private int totalResult;
	private List<ListTopupBalanceBean> topupBalance;
	
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public int getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(int totalPage) {
		this.totalPage = totalPage;
	}
	public int getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(int totalResult) {
		this.totalResult = totalResult;
	}
	public List<ListTopupBalanceBean> getTopupBalance() {
		return topupBalance;
	}
	public void setTopupBalance(List<ListTopupBalanceBean> topupBalance) {
		this.topupBalance = topupBalance;
	}
	
	
}
